import { io, Socket } from 'socket.io-client'
import { SocketStatus } from './enums'
import Logger from '../../../shared/utils/Logger'

const logger = new Logger({
  prefix: 'WS',
  enableColors: true
})

class SocketClient {
  private static instance: SocketClient
  private socket: Socket
  private status: SocketStatus

  private constructor() {
    this.connect()
  }

  public static getInstance(): SocketClient {
    if (!SocketClient.instance) {
      SocketClient.instance = new SocketClient()
    }
    return SocketClient.instance
  }

  connect(): void {
    if (this.status === SocketStatus.AUTHENTICATED) return
  }

  getSocket(): Socket {
    return this.socket
  }
}

export default SocketClient
