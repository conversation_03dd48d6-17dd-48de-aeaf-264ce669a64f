@import 'tailwindcss';

:root {
  --ev-c-white: #ffffff;
  --ev-c-white-soft: #f8f8f8;
  --ev-c-white-mute: #f2f2f2;

  --ev-c-black: #1b1b1f;
  --ev-c-black-soft: #222222;
  --ev-c-black-mute: #282828;

  --ev-c-gray-1: #515c67;
  --ev-c-gray-2: #414853;
  --ev-c-gray-3: #32363f;
  --ev-c-gray-4: #212327;

  --ev-c-text-1: rgba(255, 255, 245, 0.86);
  --ev-c-text-2: rgba(235, 235, 245, 0.6);
  --ev-c-text-3: rgba(235, 235, 245, 0.38);

  --ev-button-alt-border: transparent;
  --ev-button-alt-text: var(--ev-c-text-1);
  --ev-button-alt-bg: var(--ev-c-gray-3);
  --ev-button-alt-hover-border: transparent;
  --ev-button-alt-hover-text: var(--ev-c-text-1);
  --ev-button-alt-hover-bg: var(--ev-c-gray-2);

  /* Default (light) theme */
  --scrollbar-track: var(--ev-c-white-mute);
  --scrollbar-thumb: var(--ev-c-gray-1);

  /* Scrollbar base size */
  --scrollbar-size: 6px;
}

@media (prefers-color-scheme: dark) {
  :root {
    --scrollbar-track: var(--ev-c-gray-3);
    --scrollbar-thumb: var(--ev-c-gray-1);
  }
}

body {
  min-height: 100vh;
  color: var(--ev-c-text-1);
  background: var(--ev-c-black);
  line-height: 1.6;
  font-family:
    Inter,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Fira Sans',
    'Droid Sans',
    'Helvetica Neue',
    sans-serif;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* WebKit-based browsers */
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
}

@theme {
  --color-background: var(--ev-c-black);
  --color-background-soft: var(--ev-c-black-soft);
  --color-background-mute: var(--ev-c-black-mute);
  --color-background-alt: var(--ev-c-gray-4);

  /* border color */
  --color-border: var(--ev-c-gray-4);

  --color-text: var(--ev-c-text-1);
}

/* WebKit-based browsers */
::-webkit-scrollbar {
  width: var(--scrollbar-size);
  height: var(--scrollbar-size);
}

::-webkit-scrollbar-track {
  background-color: var(--scrollbar-track);
  border-radius: 8px;
}

::-webkit-scrollbar-thumb {
  background-color: var(--scrollbar-thumb);
  border-radius: 8px;
  border: 2px solid transparent; /* creates inner gap for sleek look */
  background-clip: padding-box;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--ev-c-gray-2);
}

::-webkit-scrollbar-thumb:active {
  background-color: var(--ev-c-gray-1);
}

/* Firefox */
/* Removing duplicate body block, as scrollbar styling is already defined within the main body rule above. */
